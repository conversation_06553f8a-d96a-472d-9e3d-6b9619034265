"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ChevronRight, Clock, MapPin, Play, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const services = [
	{
		id: 1,
		title: "Excursion en WaterBikes",
		shortDescription:
			"Optez pour une manière ludique et douce d'explorer le littoral. Les WaterBikes vous offrent une vue imprenable tout en pédalant au-dessus des eaux calmes de manière éco-responsable.",
		image: "/images/waterbikes_service.png",
		duration: "2h",
		ageLimit: "À partir de 12 ans",
		rating: 4.8,
		features: ["Collation offerte", "Sac étanche fourni", "Guide expérimenté"],
		category: "Nautique",
		difficulty: "Facile",
		reviews: 124,
		capacity: "Petits groupes",
		location: "Port de pêche, Petit-Canal",
		price: 25, // Dummy price
	},
	{
		id: 2,
		title: "Visite Guidée Culturelle",
		shortDescription:
			"Plongez au cœur de l'histoire guadeloupéenne à travers nos visites guidées des lieux emblématiques du passé. Un voyage dans le temps à la rencontre des mémoires.",
		image: "/images/cultural_tour_service.png",
		duration: "3h",
		ageLimit: "Gratuit pour les -12 ans",
		rating: 4.9,
		features: ["Collation offerte", "Chasse au trésor ludique", "Guide passionné"],
		category: "Culturel",
		difficulty: "Facile",
		reviews: 89,
		capacity: "12 pers. max",
		location: "Départ de Petit-Canal",
		price: 30, // Dummy price
	},
	{
		id: 3,
		title: "Rencontre avec les Pélicans",
		shortDescription:
			"Offrez-vous une aventure unique au cœur d'un îlet sauvage où les pélicans vous offrent un spectacle fascinant dans leur habitat naturel.",
		image: "/images/pelican_encounter_service.png",
		duration: "3h",
		ageLimit: "Gratuit pour les -6 ans",
		rating: 5.0,
		features: ["Collation offerte", "Jumelles d'observation", "Îlet sauvage"],
		category: "Nature",
		difficulty: "Facile",
		reviews: 67,
		capacity: "8 pers. max",
		location: "Départ port de Petit-Canal",
		price: 45, // Dummy price
	},
];

const testimonials = [
	{
		name: "Marie Dubois",
		location: "Paris",
		rating: 5,
		comment: "Une expérience magique ! L'équipe est passionnée et les paysages à couper le souffle.",
		service: "WaterBikes",
	},
	{
		name: "Jean-Luc Martin",
		location: "Lyon",
		rating: 5,
		comment: "Parfait pour découvrir la Guadeloupe autrement. Nos enfants ont adoré la chasse au trésor !",
		service: "Visite Culturelle",
	},
	{
		name: "Un Pélican",
		location: "Îlet Secret",
		rating: 5,
		comment: "Un endroit calme. Un excellent moment pour mieux comprendre la Guadeloupe.",
		service: "Rencontre Pélicans",
	},
];

export default function HomePage() {
	const [currentTestimonial, setCurrentTestimonial] = useState(0);

	useEffect(() => {
		const timer = setInterval(() => {
			setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
		}, 5000);
		return () => clearInterval(timer);
	}, []);

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</Link>

						<nav className="hidden md:flex items-center space-x-8">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Accueil
							</Link>
							<Link
								href="/services"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Services
							</Link>
							<Link
								href="/about"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								À propos
							</Link>
							<Link
								href="/gallery"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Galerie
							</Link>
							<Link
								href="/contact"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Contact
							</Link>
							<Link href="/reservation">
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Réserver
								</Button>
							</Link>
						</nav>

						{/* Mobile menu button */}
						<Button variant="ghost" className="md:hidden">
							<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M4 6h16M4 12h16M4 18h16"
								/>
							</svg>
						</Button>
					</div>
				</div>
			</header>

			{/* Hero Section with Video Background */}
			<section className="relative min-h-screen flex items-center justify-center overflow-hidden">
				{/* Video Background */}
				<video
					autoPlay
					muted
					loop
					playsInline
					preload="auto"
					className="absolute top-0 left-0 w-full h-full object-cover"
					style={{ zIndex: 1 }}
				>
					<source src="/videos/home-banner.mp4" type="video/mp4" />
					Your browser does not support the video tag.
				</video>

				{/* Overlay for better text readability */}
				<div className="absolute inset-0 bg-black/20" style={{ zIndex: 2 }}></div>

				<div className="container mx-auto px-4 text-center relative" style={{ zIndex: 10 }}>
					<motion.div
						initial={{ opacity: 0, y: 50 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 1 }}
						className="max-w-4xl mx-auto"
					>
						<Badge className="mb-6 bg-white/20 text-white border-white/30 backdrop-blur-sm px-4 py-2 text-lg">
							🌴 Découvrez la Guadeloupe Authentique
						</Badge>

						<h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
							L'Aventure au Cœur de la
							<span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
								Guadeloupe
							</span>
						</h1>

						<p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
							Plongez au cœur du patrimoine exceptionnel de la Guadeloupe ! Entre nature préservée, sites
							historiques et traditions culturelles vibrantes, vivez des expériences immersives où
							écologie et découverte vont de pair.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Link href="/services">
								<Button
									size="lg"
									className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									<Play className="w-5 h-5 mr-2" />
									Découvrir nos Excursions
								</Button>
							</Link>
							<Link href="/reservation">
								<Button
									size="lg"
									variant="outline"
									className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
								>
									Réserver Maintenant
									<ChevronRight className="w-5 h-5 ml-2" />
								</Button>
							</Link>
						</div>
					</motion.div>
				</div>

				{/* Floating elements */}
				<div className="absolute top-20 left-10 animate-bounce">
					<div className="w-4 h-4 bg-yellow-400 rounded-full opacity-70"></div>
				</div>
				<div className="absolute top-40 right-20 animate-pulse">
					<div className="w-6 h-6 bg-emerald-400 rounded-full opacity-60"></div>
				</div>
				<div className="absolute bottom-40 left-20 animate-bounce delay-1000">
					<div className="w-3 h-3 bg-orange-400 rounded-full opacity-80"></div>
				</div>
			</section>

			{/* Services Section */}
			<section className="py-20 bg-white">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 border-emerald-200 px-4 py-2">
							🏝️ Nos Expériences
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Aventures Authentiques
							<span className="block text-emerald-600">et Inoubliables</span>
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Entre excursions en WaterBike, balades en pleine nature et découvertes culturelles, vivez
							des moments hors du commun au rythme des paysages guadeloupéens.
						</p>
					</motion.div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{services.map((service, index) => (
							<motion.div
								key={service.id}
								initial={{ opacity: 0, y: 50 }}
								whileInView={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.6, delay: index * 0.2 }}
								whileHover={{ y: -10 }}
								className="group"
							>
								<Card className="overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50">
									<div className="relative overflow-hidden">
										<Image
											src={service.image || "/placeholder.svg"}
											alt={service.title}
											width={400}
											height={300}
											className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
										/>
										<div className="absolute top-4 left-4">
											<Badge className="bg-white/90 text-emerald-700 font-semibold">
												{service.ageLimit}
											</Badge>
										</div>
										<div className="absolute top-4 right-4 flex items-center bg-white/90 rounded-full px-2 py-1">
											<Star className="w-4 h-4 text-yellow-400 fill-current" />
											<span className="text-sm font-semibold ml-1">{service.rating}</span>
										</div>
									</div>

									<CardContent className="p-6">
										<h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors">
											{service.title}
										</h3>
										<p className="text-gray-600 mb-4 leading-relaxed">{service.shortDescription}</p>

										<div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
											<div className="flex items-center">
												<Clock className="w-4 h-4 mr-1" />
												{service.duration}
											</div>
											<div className="flex items-center">
												<Users className="w-4 h-4 mr-1" />
												Petits groupes
											</div>
										</div>

										<div className="space-y-2 mb-6">
											{service.features.map((feature, idx) => (
												<div key={idx} className="flex items-center text-sm text-emerald-600">
													<div className="w-2 h-2 bg-emerald-400 rounded-full mr-2"></div>
													{feature}
												</div>
											))}
										</div>

										<Link href={`/reservation?service=${service.id}`}>
											<Button className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white rounded-full py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
												Réserver Maintenant
												<ChevronRight className="w-4 h-4 ml-2" />
											</Button>
										</Link>
									</CardContent>
								</Card>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* Testimonials Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-50 to-teal-50">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-700 border-orange-200 px-4 py-2">
							⭐ Témoignages
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Ce Que Disent Nos
							<span className="block text-emerald-600">Aventuriers</span>
						</h2>
					</motion.div>

					<div className="max-w-4xl mx-auto">
						<motion.div
							key={currentTestimonial}
							initial={{ opacity: 0, x: 50 }}
							animate={{ opacity: 1, x: 0 }}
							exit={{ opacity: 0, x: -50 }}
							transition={{ duration: 0.5 }}
							className="text-center"
						>
							<Card className="p-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
								<CardContent className="p-0">
									<div className="flex justify-center mb-4">
										{[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
											<Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
										))}
									</div>
									<blockquote className="text-xl md:text-2xl text-gray-700 mb-6 italic leading-relaxed">
										"{testimonials[currentTestimonial].comment}"
									</blockquote>
									<div className="flex items-center justify-center space-x-4">
										<div className="text-center">
											<p className="font-semibold text-gray-900 text-lg">
												{testimonials[currentTestimonial].name}
											</p>
											<div className="flex items-center justify-center text-gray-500 text-sm">
												<MapPin className="w-4 h-4 mr-1" />
												{testimonials[currentTestimonial].location}
											</div>
											<Badge
												variant="outline"
												className="mt-2 text-emerald-600 border-emerald-200"
											>
												{testimonials[currentTestimonial].service}
											</Badge>
										</div>
									</div>
								</CardContent>
							</Card>
						</motion.div>

						<div className="flex justify-center mt-8 space-x-2">
							{testimonials.map((_, index) => (
								<button
									key={index}
									onClick={() => setCurrentTestimonial(index)}
									className={`w-3 h-3 rounded-full transition-all duration-300 ${
										index === currentTestimonial
											? "bg-emerald-500 w-8"
											: "bg-gray-300 hover:bg-gray-400"
									}`}
								/>
							))}
						</div>
					</div>
				</div>
			</section>

			{/* About Preview Section */}
			<section className="py-20 bg-white">
				<div className="container mx-auto px-4">
					<div className="grid lg:grid-cols-2 gap-12 items-center">
						<motion.div
							initial={{ opacity: 0, x: -50 }}
							whileInView={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.8 }}
						>
							<Badge className="mb-4 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 border-blue-200 px-4 py-2">
								🌊 Notre Histoire
							</Badge>
							<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
								Une Passion Héritée de
								<span className="block text-emerald-600">l'Enfance</span>
							</h2>
							<p className="text-lg text-gray-600 mb-6 leading-relaxed">
								Notre passion pour cette île, héritée d'une enfance bercée par la mer et la terre, nous
								a poussés à créer des expériences immersives où écologie et découverte vont de pair.
							</p>
							<p className="text-lg text-gray-600 mb-8 leading-relaxed">
								Partez avec nous à la rencontre des trésors cachés de l'archipel, dans le respect de
								l'environnement et des traditions locales. Chaque excursion est pensée pour minimiser
								l'impact sur l'environnement et sensibiliser à la préservation de notre patrimoine
								naturel.
							</p>
							<Link href="/about">
								<Button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Découvrir Notre Histoire
									<ChevronRight className="w-4 h-4 ml-2" />
								</Button>
							</Link>
						</motion.div>

						<motion.div
							initial={{ opacity: 0, x: 50 }}
							whileInView={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.8 }}
							className="relative"
						>
							<div className="relative overflow-hidden rounded-2xl shadow-2xl">
								<Image
									src="/images/team_about_preview.png"
									alt="Notre équipe"
									width={500}
									height={600}
									className="w-full h-96 object-cover"
								/>
								<div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
								<div className="absolute bottom-6 left-6 text-white">
									<p className="text-lg font-semibold">Une équipe passionnée</p>
									<p className="text-sm opacity-90">Guides locaux expérimentés</p>
								</div>
							</div>

							{/* Floating stats */}
							<div className="absolute -top-6 -right-6 bg-white rounded-2xl shadow-xl p-6 border-4 border-emerald-100">
								<div className="text-center">
									<div className="text-3xl font-bold text-emerald-600">100%</div>
									<div className="text-sm text-gray-600">Éco-responsable</div>
								</div>
							</div>

							<div className="absolute -bottom-6 -left-6 bg-white rounded-2xl shadow-xl p-6 border-4 border-orange-100">
								<div className="text-center">
									<div className="text-3xl font-bold text-orange-600">5★</div>
									<div className="text-sm text-gray-600">Satisfaction client</div>
								</div>
							</div>
						</motion.div>
					</div>
				</div>
			</section>

			{/* Eco-Responsibility Section */}
			<section className="py-20 bg-gradient-to-br from-green-50 to-emerald-50">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<Badge className="mb-4 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200 px-4 py-2">
							🌿 La Préservation est Primordiale
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Explorations
							<span className="block text-emerald-600">Éco-Responsables</span>
						</h2>
						<p className="text-xl text-gray-600 mb-8 leading-relaxed">
							En Guadeloupe, chaque site est un joyau à préserver. Chez Soleil & Découverte, nous nous
							engageons à faire rayonner notre île tout en protégeant ses richesses naturelles et
							culturelles. Ensemble, préservons aujourd'hui pour émerveiller demain.
						</p>
						<p className="text-lg text-gray-600 leading-relaxed">
							Naviguez à travers la mangrove, longez le littoral et laissez-vous émerveiller par une
							biodiversité exceptionnelle. Nos visites sont pensées pour minimiser l'impact sur
							l'environnement, respecter la biodiversité et sensibiliser à la préservation de notre
							patrimoine naturel.
						</p>
					</motion.div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-600 to-teal-700 relative overflow-hidden">
				<div className="absolute inset-0 bg-black/20"></div>
				<Image src="/images/sunset_cta.png" alt="Sunset" fill className="object-cover -z-10" />

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<h2 className="text-4xl md:text-6xl font-bold text-white mb-6">Prêt pour l'Aventure ?</h2>
						<p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
							Réservez dès maintenant votre excursion et découvrez la Guadeloupe comme vous ne l'avez
							jamais vue !
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/reservation">
								<Button
									size="lg"
									className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									Réserver Maintenant
									<ChevronRight className="w-5 h-5 ml-2" />
								</Button>
							</Link>
							<Button
								size="lg"
								variant="outline"
								className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
							>
								Voir Toutes les Excursions
							</Button>
						</div>
					</motion.div>
				</div>
			</section>

			{/* Footer */}
			<footer className="bg-gray-900 text-white py-16">
				<div className="container mx-auto px-4">
					<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
						<div>
							<div className="flex items-center space-x-3 mb-6">
								<Image
									src="/images/logo-hd.png"
									alt="Soleil & Découverte"
									width={50}
									height={50}
									className="object-contain"
								/>
								<div>
									<h3 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
										Soleil & Découverte
									</h3>
									<p className="text-sm text-emerald-400">L'aventure au cœur de la Guadeloupe</p>
								</div>
							</div>
							<p className="text-gray-400 leading-relaxed">
								Découvrez la Guadeloupe authentique avec nos excursions éco-responsables, entre nature
								préservée et traditions culturelles.
							</p>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Navigation</h4>
							<ul className="space-y-2">
								<li>
									<Link href="/" className="text-gray-400 hover:text-white transition-colors">
										Accueil
									</Link>
								</li>
								<li>
									<Link href="/services" className="text-gray-400 hover:text-white transition-colors">
										Services
									</Link>
								</li>
								<li>
									<Link href="/about" className="text-gray-400 hover:text-white transition-colors">
										À propos
									</Link>
								</li>
								<li>
									<Link href="/gallery" className="text-gray-400 hover:text-white transition-colors">
										Galerie
									</Link>
								</li>
								<li>
									<Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
										Contact
									</Link>
								</li>
							</ul>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Nos Activités</h4>
							<ul className="space-y-2">
								<li>
									<span className="text-gray-400">Excursions WaterBikes</span>
								</li>
								<li>
									<span className="text-gray-400">Visites Culturelles</span>
								</li>
								<li>
									<span className="text-gray-400">Rencontre Pélicans</span>
								</li>
								<li>
									<span className="text-gray-400">Dégustations Locales</span>
								</li>
							</ul>
						</div>

						<div>
							<h4 className="text-lg font-semibold mb-4 text-emerald-400">Contact</h4>
							<div className="space-y-3 text-gray-400">
								<div className="flex items-center">
									<MapPin className="w-4 h-4 mr-2 text-emerald-400" />
									<span className="text-sm">Petit-Canal, Guadeloupe</span>
								</div>
								<div className="flex items-center">
									<svg
										className="w-4 h-4 mr-2 text-emerald-400"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
									</svg>
									<span className="text-sm">+33 6 40 24 44 25</span>
								</div>
								<div className="flex items-center">
									<svg
										className="w-4 h-4 mr-2 text-emerald-400"
										fill="currentColor"
										viewBox="0 0 20 20"
									>
										<path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
										<path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
									</svg>
									<span className="text-sm"><EMAIL></span>
								</div>
							</div>
						</div>
					</div>

					<div className="border-t border-gray-800 mt-12 pt-8 text-center">
						<p className="text-gray-400">
							© 2024 Soleil & Découverte. Tous droits réservés. |
							<Link href="/mentions-legales" className="hover:text-white transition-colors ml-1">
								Mentions légales
							</Link>{" "}
							|
							<Link href="/confidentialite" className="hover:text-white transition-colors ml-1">
								Confidentialité
							</Link>
						</p>
					</div>
				</div>
			</footer>
		</div>
	);
}
