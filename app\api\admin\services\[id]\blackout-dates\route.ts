import { withAdminAuth } from '@/lib/admin-auth'
import { supabaseAdmin } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/admin/services/[id]/blackout-dates - Get blackout dates for a service
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id

    const { data: blackoutDates, error } = await supabaseAdmin
      .from('service_blackout_dates')
      .select('*')
      .eq('service_id', serviceId)
      .order('start_date', { ascending: true })

    if (error) {
      console.error('Error fetching blackout dates:', error)
      return NextResponse.json({ error: 'Failed to fetch blackout dates' }, { status: 500 })
    }

    return NextResponse.json({ blackoutDates })
  } catch (error) {
    console.error('Blackout dates GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:read')

// POST /api/admin/services/[id]/blackout-dates - Create new blackout date
export const POST = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id
    const blackoutData = await request.json()

    // Validate required fields
    if (!blackoutData.start_date || !blackoutData.end_date || !blackoutData.reason) {
      return NextResponse.json(
        { error: 'Start date, end date, and reason are required' },
        { status: 400 }
      )
    }

    // Validate date range
    if (new Date(blackoutData.start_date) > new Date(blackoutData.end_date)) {
      return NextResponse.json(
        { error: 'Start date must be before or equal to end date' },
        { status: 400 }
      )
    }

    // Ensure service_id matches the URL parameter
    blackoutData.service_id = serviceId

    const { data: blackoutDate, error } = await supabaseAdmin
      .from('service_blackout_dates')
      .insert(blackoutData)
      .select()
      .single()

    if (error) {
      console.error('Error creating blackout date:', error)
      return NextResponse.json({ error: 'Failed to create blackout date' }, { status: 500 })
    }

    return NextResponse.json({ blackoutDate }, { status: 201 })
  } catch (error) {
    console.error('Blackout dates POST error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')
