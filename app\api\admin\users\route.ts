import { logAdminAction, withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Profile = Database["public"]["Tables"]["profiles"]["Row"];
type ProfileInsert = Database["public"]["Tables"]["profiles"]["Insert"];
type ProfileUpdate = Database["public"]["Tables"]["profiles"]["Update"];

// GET /api/admin/users - List all users from profiles table
export const GET = withAdminAuth(async (request: NextRequest) => {
	try {
		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get("page") || "1");
		const limit = parseInt(searchParams.get("limit") || "20");
		const search = searchParams.get("search");
		const role = searchParams.get("role");
		const offset = (page - 1) * limit;

		// Build query
		let query = supabaseAdmin
			.from("profiles")
			.select("*")
			.order("created_at", { ascending: false })
			.range(offset, offset + limit - 1);

		// Apply search filter
		if (search) {
			query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%`);
		}

		// Apply role filter
		if (role) {
			query = query.eq("role", role);
		}

		const { data: users, error, count } = await query;

		if (error) {
			console.error("Error fetching users:", error);
			return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 });
		}

		// Get total count for pagination
		const { count: totalCount } = await supabaseAdmin.from("profiles").select("*", { count: "exact", head: true });

		return NextResponse.json({
			users: users || [],
			pagination: {
				page,
				limit,
				total: totalCount || 0,
				totalPages: Math.ceil((totalCount || 0) / limit),
			},
		});
	} catch (error) {
		console.error("Users GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "users:read");

// POST /api/admin/users - Create new user
export const POST = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const userData: ProfileInsert = await request.json();

		// Validate required fields
		if (!userData.first_name || !userData.last_name || !userData.email) {
			return NextResponse.json(
				{ error: "Missing required fields: first_name, last_name, email" },
				{ status: 400 }
			);
		}

		// Check if email already exists
		const { data: existingUser } = await supabaseAdmin
			.from("profiles")
			.select("id")
			.eq("email", userData.email)
			.single();

		if (existingUser) {
			return NextResponse.json({ error: "User with this email already exists" }, { status: 400 });
		}

		// Create user in profiles table
		const { data: newUser, error } = await supabaseAdmin.from("profiles").insert(userData).select().single();

		if (error) {
			console.error("Error creating user:", error);
			return NextResponse.json({ error: "Failed to create user" }, { status: 500 });
		}

		// Log admin action
		await logAdminAction(user.id, "CREATE", "profiles", newUser.id, null, newUser, request);

		return NextResponse.json({ user: newUser }, { status: 201 });
	} catch (error) {
		console.error("User POST error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "users:write");

// PUT /api/admin/users - Bulk update users
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
	try {
		const { userIds, updates }: { userIds: string[]; updates: ProfileUpdate } = await request.json();

		if (!userIds || userIds.length === 0) {
			return NextResponse.json({ error: "No user IDs provided" }, { status: 400 });
		}

		// Get users for audit log before update
		const { data: usersToUpdate } = await supabaseAdmin.from("profiles").select("*").in("id", userIds);

		// Update users
		const { data: updatedUsers, error } = await supabaseAdmin
			.from("profiles")
			.update(updates)
			.in("id", userIds)
			.select();

		if (error) {
			console.error("Error updating users:", error);
			return NextResponse.json({ error: "Failed to update users" }, { status: 500 });
		}

		// Log admin action for each user
		for (const originalUser of usersToUpdate || []) {
			const updatedUser = updatedUsers?.find((u) => u.id === originalUser.id);
			if (updatedUser) {
				await logAdminAction(
					user.id,
					"UPDATE",
					"profiles",
					originalUser.id,
					originalUser,
					updatedUser,
					request
				);
			}
		}

		return NextResponse.json({
			users: updatedUsers,
			updated: updatedUsers?.length || 0,
		});
	} catch (error) {
		console.error("Users PUT error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "users:write");
