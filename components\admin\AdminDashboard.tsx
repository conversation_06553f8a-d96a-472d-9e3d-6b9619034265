"use client";

import { adminApi } from '@/lib/api-client';
import { Activity, AlertCircle, Calendar, Clock, DollarSign, Loader2, TrendingUp, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

interface AnalyticsData {
  kpis: {
    totalReservations: { value: number; change: number; trend: string };
    totalRevenue: { value: number; change: number; trend: string; currency: string };
    totalParticipants: { value: number; change: number; trend: string };
    occupancyRate: { value: number; trend: string };
    newCustomers: { value: number };
  };
  summary: {
    totalReservations: number;
    confirmedReservations: number;
    totalRevenue: number;
    totalParticipants: number;
    newCustomers: number;
  };
}

const AdminDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await adminApi.getAnalytics({ period: '30' });
        setAnalyticsData(data);
      } catch (err) {
        console.error('Error fetching analytics:', err);
        setError('Erreur lors du chargement des données');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatChange = (change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(1)}%`;
  };

  const getStats = () => {
    if (!analyticsData) return [];

    return [
      {
        title: 'Réservations ce mois',
        value: analyticsData.kpis.totalReservations.value.toString(),
        change: formatChange(analyticsData.kpis.totalReservations.change),
        changeType: analyticsData.kpis.totalReservations.trend === 'up' ? 'positive' : 'negative',
        icon: Calendar
      },
      {
        title: 'Chiffre d\'affaires',
        value: formatCurrency(analyticsData.kpis.totalRevenue.value),
        change: formatChange(analyticsData.kpis.totalRevenue.change),
        changeType: analyticsData.kpis.totalRevenue.trend === 'up' ? 'positive' : 'negative',
        icon: DollarSign
      },
      {
        title: 'Nouveaux clients',
        value: analyticsData.kpis.newCustomers.value.toString(),
        change: '',
        changeType: 'neutral',
        icon: Users
      },
      {
        title: 'Taux d\'occupation',
        value: `${analyticsData.kpis.occupancyRate.value.toFixed(1)}%`,
        change: '',
        changeType: 'neutral',
        icon: TrendingUp
      }
    ];
  };

  const stats = getStats();

  const [recentBookings, setRecentBookings] = useState<any[]>([]);
  const [upcomingActivities, setUpcomingActivities] = useState<any[]>([]);

  useEffect(() => {
    const fetchRecentData = async () => {
      try {
        // Fetch recent reservations
        const reservationsData = await adminApi.getReservations({
          limit: 5,
          page: 1,
          sort: 'created_at',
          order: 'desc'
        });

        if (reservationsData?.reservations) {
          const formattedBookings = reservationsData.reservations.map((reservation: any) => ({
            id: reservation.id,
            customer: `${reservation.customer_profile?.first_name || ''} ${reservation.customer_profile?.last_name || ''}`.trim() || 'Client inconnu',
            service: reservation.service?.name || 'Service inconnu',
            date: new Date(reservation.start_time).toLocaleDateString('fr-FR'),
            time: new Date(reservation.start_time).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }),
            status: reservation.status,
            amount: formatCurrency(reservation.total_amount || 0)
          }));
          setRecentBookings(formattedBookings);
        }

        // Fetch today's activities
        const today = new Date().toISOString().split('T')[0];
        const todayActivities = await adminApi.getReservations({
          limit: 10,
          page: 1,
          date_from: today,
          date_to: today,
          status: 'confirmed'
        });

        if (todayActivities?.reservations) {
          const formattedActivities = todayActivities.reservations.map((reservation: any) => ({
            time: new Date(reservation.start_time).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' }),
            service: reservation.service?.name || 'Service inconnu',
            guide: reservation.assigned_employee ?
              `${reservation.assigned_employee.first_name} ${reservation.assigned_employee.last_name}` :
              'Guide non assigné',
            participants: reservation.participant_count || 0
          }));
          setUpcomingActivities(formattedActivities);
        }
      } catch (err) {
        console.error('Error fetching recent data:', err);
      }
    };

    if (analyticsData) {
      fetchRecentData();
    }
  }, [analyticsData]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Tableau de bord</h1>
          <p className="text-gray-600">Vue d'ensemble de votre activité</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
            <span className="text-gray-600">Chargement des données...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Tableau de bord</h1>
          <p className="text-gray-600">Vue d'ensemble de votre activité</p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-6 w-6" />
            <span>{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Tableau de bord</h1>
        <p className="text-gray-600">Vue d'ensemble de votre activité</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="bg-emerald-100 p-2 rounded-lg">
                  <Icon className="h-6 w-6 text-emerald-600" />
                </div>
                {stat.change && (
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600' :
                    stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {stat.change}
                  </span>
                )}
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
                <p className="text-sm text-gray-600">{stat.title}</p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Bookings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">Réservations récentes</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{booking.customer}</p>
                    <p className="text-sm text-gray-600">{booking.service}</p>
                    <p className="text-xs text-gray-500">{booking.date} à {booking.time}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900">{booking.amount}</p>
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      booking.status === 'confirmed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {booking.status === 'confirmed' ? 'Confirmé' : 'En attente'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Today's Activities */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">Activités d'aujourd'hui</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {upcomingActivities.map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="bg-emerald-100 p-2 rounded-lg">
                    <Clock className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.time}</p>
                    <p className="text-sm text-gray-600">{activity.service}</p>
                    <p className="text-xs text-gray-500">Guide: {activity.guide}</p>
                  </div>
                  <div className="text-right">
                    <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                      {activity.participants} pers.
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Actions rapides</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 text-left bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-colors">
            <Activity className="h-6 w-6 text-emerald-600 mb-2" />
            <p className="font-medium text-gray-900">Nouvelle réservation</p>
            <p className="text-sm text-gray-600">Créer une réservation manuelle</p>
          </button>
          <button className="p-4 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
            <Calendar className="h-6 w-6 text-blue-600 mb-2" />
            <p className="font-medium text-gray-900">Gérer le planning</p>
            <p className="text-sm text-gray-600">Modifier les créneaux disponibles</p>
          </button>
          <button className="p-4 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
            <Users className="h-6 w-6 text-purple-600 mb-2" />
            <p className="font-medium text-gray-900">Contacter un client</p>
            <p className="text-sm text-gray-600">Envoyer un message personnalisé</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
