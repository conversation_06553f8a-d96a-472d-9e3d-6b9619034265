import { NotificationProvider } from "@/contexts/NotificationContext";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: "Administration - Soleil et Découverte",
	description: "Interface d'administration pour Soleil et Découverte",
};

export default function AdminLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<NotificationProvider>
			<div className="min-h-screen bg-gray-50">
				{children}
			</div>
		</NotificationProvider>
	);
}
