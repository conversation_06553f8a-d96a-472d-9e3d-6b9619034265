// Test script to verify Supabase connection and data
import { supabase } from './supabase'

export async function testDatabaseConnection() {
  try {
    console.log('🔍 Testing Supabase connection...')
    
    // Test 1: Basic connection
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('*')
      .limit(1)
    
    if (servicesError) {
      throw new Error(`Services query failed: ${servicesError.message}`)
    }
    
    console.log('✅ Basic connection successful')
    
    // Test 2: Services with pricing tiers
    const { data: servicesWithPricing, error: pricingError } = await supabase
      .from('services')
      .select(`
        *,
        pricing_tiers (*)
      `)
      .eq('is_active', true)
      .limit(3)
    
    if (pricingError) {
      throw new Error(`Pricing query failed: ${pricingError.message}`)
    }
    
    console.log('✅ Services with pricing tiers:', servicesWithPricing?.length)
    
    // Test 3: Equipment data
    const { data: equipment, error: equipmentError } = await supabase
      .from('equipment')
      .select('*')
      .eq('is_active', true)
    
    if (equipmentError) {
      throw new Error(`Equipment query failed: ${equipmentError.message}`)
    }
    
    console.log('✅ Equipment data:', equipment?.length)
    
    // Test 4: Service equipment requirements
    const { data: requirements, error: requirementsError } = await supabase
      .from('service_equipment_requirements')
      .select(`
        *,
        service:services(*),
        equipment:equipment(*)
      `)
      .limit(5)
    
    if (requirementsError) {
      throw new Error(`Requirements query failed: ${requirementsError.message}`)
    }
    
    console.log('✅ Service equipment requirements:', requirements?.length)
    
    // Summary
    console.log('\n📊 Database Summary:')
    console.log(`- Services: ${servicesWithPricing?.length || 0}`)
    console.log(`- Equipment types: ${equipment?.length || 0}`)
    console.log(`- Equipment requirements: ${requirements?.length || 0}`)
    
    return {
      success: true,
      data: {
        services: servicesWithPricing,
        equipment,
        requirements
      }
    }
    
  } catch (error) {
    console.error('❌ Database connection test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Helper function to get sample service with full details
export async function getSampleServiceWithDetails(serviceId?: string) {
  try {
    const query = supabase
      .from('services')
      .select(`
        *,
        pricing_tiers (*),
        service_equipment_requirements (
          *,
          equipment (*)
        )
      `)
      .eq('is_active', true)
    
    if (serviceId) {
      query.eq('id', serviceId)
    } else {
      query.limit(1)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(`Service query failed: ${error.message}`)
    }
    
    return { data: data?.[0] || null, error: null }
    
  } catch (error) {
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
