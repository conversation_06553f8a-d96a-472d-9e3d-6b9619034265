import type { AdminAuditLog, BusinessSettings } from "./admin";
import type { Profiles } from "./auth";
import type { CustomerAnalytics, CustomerFeedback, CustomerJourneyEvents, Customers } from "./customers";
import type {
	EmployeeAnalytics,
	EmployeeAvailability,
	Employees,
	EmployeeServiceQualifications,
	EmployeeTimeOff,
} from "./employees";
import type { Equipment, EquipmentReservations, EquipmentUtilizationHistory } from "./equipments";
import type { Notifications } from "./notifications";
import type { Payments, Refunds, Reservations, ReservationStatusHistory } from "./reservations";
import type {
	PricingTiers,
	ScheduleTemplates,
	ServiceAnalytics,
	ServiceBlackoutDates,
	ServiceEquipmentRequirements,
	Services,
	ServiceSchedulingRules,
} from "./services";

export interface Database {
	public: {
		Tables: {
			// admin
			admin_audit_log: AdminAuditLog;
			business_settings: BusinessSettings;

			// customers
			customers: Customers;
			customer_feedback: CustomerFeedback;
			customer_analytics: CustomerAnalytics;
			customer_journey_events: CustomerJourneyEvents;

			// employees
			employees: Employees;
			employee_availability: EmployeeAvailability;
			employee_time_off: EmployeeTimeOff;
			employee_analytics: EmployeeAnalytics;
			employee_service_qualifications: EmployeeServiceQualifications;

			// reservations
			reservations: Reservations;
			reservation_status_history: ReservationStatusHistory;
			payments: Payments;
			refunds: Refunds;

			// services
			services: Services;
			pricing_tiers: PricingTiers;
			service_scheduling_rules: ServiceSchedulingRules;
			schedule_templates: ScheduleTemplates;
			service_analytics: ServiceAnalytics;
			service_blackout_dates: ServiceBlackoutDates;
			service_equipment_requirements: ServiceEquipmentRequirements;

			// equipment
			equipment: Equipment;
			equipment_reservations: EquipmentReservations;
			equipment_utilization_history: EquipmentUtilizationHistory;

			// notifications
			notifications: Notifications;

			// auth
			profiles: Profiles;
		};
	};
}
