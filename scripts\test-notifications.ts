/**
 * Test script for notification functionality
 * Run with: npx tsx scripts/test-notifications.ts
 */

import { supabaseAdmin } from '../lib/supabase'
import { 
  createNotification, 
  createBookingConfirmationNotification,
  createPaymentReceivedNotification,
  createCancellationNotification,
  createSystemMaintenanceNotification,
  createWeatherAlertNotification,
  createCustomerMessageNotification
} from '../lib/notifications'

async function createTestNotifications() {
  console.log('🧪 Creating test notifications...')

  try {
    // Get admin users to notify
    const { data: adminProfiles, error: adminError } = await supabaseAdmin
      .from('profiles')
      .select('id, first_name, last_name')
      .eq('role', 'admin')
      .limit(1)

    if (adminError) {
      console.error('Error fetching admin profiles:', adminError)
      return
    }

    if (!adminProfiles || adminProfiles.length === 0) {
      console.log('❌ No admin profiles found. Creating a test admin profile...')
      
      // Create a test admin profile
      const { data: testAdmin, error: createError } = await supabaseAdmin
        .from('profiles')
        .insert({
          first_name: 'Test',
          last_name: 'Admin',
          email: '<EMAIL>',
          role: 'admin'
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating test admin:', createError)
        return
      }

      adminProfiles.push(testAdmin)
    }

    const adminId = adminProfiles[0].id
    console.log(`📧 Creating notifications for admin: ${adminProfiles[0].first_name} ${adminProfiles[0].last_name}`)

    // Test 1: Booking confirmation notification
    console.log('1. Creating booking confirmation notification...')
    const bookingNotification = createBookingConfirmationNotification(
      'Marie Dupont',
      'Découverte de la Mangrove',
      '15/02/2025',
      'test-reservation-1'
    )
    const booking = await createNotification(adminId, bookingNotification, 'test-reservation-1')
    console.log('✅ Booking notification created:', booking?.id)

    // Test 2: Payment received notification
    console.log('2. Creating payment notification...')
    const paymentNotification = createPaymentReceivedNotification(
      180,
      '€',
      'test-reservation-2'
    )
    const payment = await createNotification(adminId, paymentNotification, 'test-reservation-2')
    console.log('✅ Payment notification created:', payment?.id)

    // Test 3: Cancellation notification
    console.log('3. Creating cancellation notification...')
    const cancellationNotification = createCancellationNotification(
      'Jean Martin',
      'Sortie Pêche',
      '20/02/2025',
      'Conditions météorologiques défavorables'
    )
    const cancellation = await createNotification(adminId, cancellationNotification, 'test-reservation-3')
    console.log('✅ Cancellation notification created:', cancellation?.id)

    // Test 4: System maintenance notification
    console.log('4. Creating system maintenance notification...')
    const maintenanceNotification = createSystemMaintenanceNotification(
      '02:00',
      '04:00',
      'Mise à jour de sécurité'
    )
    const maintenance = await createNotification(adminId, maintenanceNotification)
    console.log('✅ Maintenance notification created:', maintenance?.id)

    // Test 5: Weather alert notification
    console.log('5. Creating weather alert notification...')
    const weatherNotification = createWeatherAlertNotification(
      'Vents forts',
      'demain',
      'Annulation possible des sorties en mer'
    )
    const weather = await createNotification(adminId, weatherNotification)
    console.log('✅ Weather notification created:', weather?.id)

    // Test 6: Customer message notification
    console.log('6. Creating customer message notification...')
    const messageNotification = createCustomerMessageNotification(
      'Sophie Leblanc',
      'Question sur l\'équipement',
      'test-reservation-4'
    )
    const message = await createNotification(adminId, messageNotification, 'test-reservation-4')
    console.log('✅ Message notification created:', message?.id)

    // Test 7: High priority notification
    console.log('7. Creating high priority notification...')
    const urgentNotification = {
      type: 'system',
      subject: 'URGENT: Problème technique',
      content: 'Un problème technique critique nécessite une attention immédiate.',
      priority: 'high' as const
    }
    const urgent = await createNotification(adminId, urgentNotification)
    console.log('✅ Urgent notification created:', urgent?.id)

    console.log('\n🎉 All test notifications created successfully!')
    console.log('📊 Check the admin notifications page to see them.')

  } catch (error) {
    console.error('❌ Error creating test notifications:', error)
  }
}

async function testNotificationQueries() {
  console.log('\n🔍 Testing notification queries...')

  try {
    // Test fetching all notifications
    const { data: allNotifications, error: allError } = await supabaseAdmin
      .from('notifications')
      .select(`
        *,
        recipient:profiles!recipient_id(first_name, last_name, email)
      `)
      .order('created_at', { ascending: false })
      .limit(10)

    if (allError) {
      console.error('Error fetching all notifications:', allError)
      return
    }

    console.log(`📋 Found ${allNotifications?.length || 0} notifications`)

    // Test fetching unread notifications
    const { data: unreadNotifications, error: unreadError } = await supabaseAdmin
      .from('notifications')
      .select('*')
      .eq('is_read', false)

    if (unreadError) {
      console.error('Error fetching unread notifications:', unreadError)
      return
    }

    console.log(`📬 Found ${unreadNotifications?.length || 0} unread notifications`)

    // Test fetching by type
    const { data: bookingNotifications, error: bookingError } = await supabaseAdmin
      .from('notifications')
      .select('*')
      .eq('notification_type', 'booking_confirmation')

    if (bookingError) {
      console.error('Error fetching booking notifications:', bookingError)
      return
    }

    console.log(`🎫 Found ${bookingNotifications?.length || 0} booking notifications`)

    // Test fetching by priority
    const { data: highPriorityNotifications, error: priorityError } = await supabaseAdmin
      .from('notifications')
      .select('*')
      .eq('priority', 'high')

    if (priorityError) {
      console.error('Error fetching high priority notifications:', priorityError)
      return
    }

    console.log(`🚨 Found ${highPriorityNotifications?.length || 0} high priority notifications`)

    console.log('✅ All notification queries completed successfully!')

  } catch (error) {
    console.error('❌ Error testing notification queries:', error)
  }
}

async function main() {
  console.log('🚀 Starting notification system tests...\n')
  
  await createTestNotifications()
  await testNotificationQueries()
  
  console.log('\n✨ Notification tests completed!')
  console.log('🌐 Visit the admin panel to see the notifications in action.')
}

// Run the tests
main().catch(console.error)
