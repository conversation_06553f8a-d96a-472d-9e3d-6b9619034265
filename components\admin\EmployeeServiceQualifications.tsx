"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { adminApi } from "@/lib/api-client";
import { CheckCircle, Plus, Trash2, User, XCircle } from "lucide-react";
import { useEffect, useState } from "react";

interface Employee {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
}

interface Service {
	id: string;
	name: string;
}

interface EmployeeServiceQualification {
	id: string;
	employee_id: string;
	service_id: string;
	is_active: boolean;
	created_at: string;
	employee?: Employee;
	service?: Service;
}

export default function EmployeeServiceQualifications() {
	const [qualifications, setQualifications] = useState<EmployeeServiceQualification[]>([]);
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [services, setServices] = useState<Service[]>([]);
	const [loading, setLoading] = useState(true);
	const [addingToEmployee, setAddingToEmployee] = useState<string | null>(null);
	const [selectedService, setSelectedService] = useState<string>("");

	useEffect(() => {
		fetchData();
	}, []);

	const fetchData = async () => {
		try {
			const [qualData, empData, servData] = await Promise.all([
				adminApi.getEmployeeServiceQualifications(),
				adminApi.getEmployees({ limit: 100 }),
				adminApi.getServices({ limit: 100 }),
			]);

			setQualifications(qualData.qualifications || []);
			setEmployees(empData.employees || []);
			setServices(servData.services || []);
		} catch (error) {
			console.error("Error fetching data:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleAddService = async (employeeId: string) => {
		if (!selectedService) return;

		try {
			await adminApi.createEmployeeServiceQualification({
				employee_id: employeeId,
				service_id: selectedService,
				is_active: true,
			});

			await fetchData();
			setAddingToEmployee(null);
			setSelectedService("");
		} catch (error) {
			console.error("Error adding service qualification:", error);
		}
	};

	const handleToggleActive = async (qualificationId: string, isActive: boolean) => {
		try {
			await adminApi.updateEmployeeServiceQualification(qualificationId, { is_active: isActive });
			await fetchData();
		} catch (error) {
			console.error("Error updating qualification:", error);
		}
	};

	const handleDelete = async (qualificationId: string) => {
		if (confirm("Êtes-vous sûr de vouloir supprimer cette qualification ?")) {
			try {
				await adminApi.deleteEmployeeServiceQualification(qualificationId);
				await fetchData();
			} catch (error) {
				console.error("Error deleting qualification:", error);
			}
		}
	};

	const getEmployeeName = (employeeId: string) => {
		const employee = employees.find((e) => e.id === employeeId);
		return employee ? `${employee.first_name} ${employee.last_name}` : "Employé inconnu";
	};

	const getServiceName = (serviceId: string) => {
		const service = services.find((s) => s.id === serviceId);
		return service ? service.name : "Service inconnu";
	};

	const getAvailableServices = (employeeId: string) => {
		const employeeQualifications = qualifications.filter((q) => q.employee_id === employeeId);
		const assignedServiceIds = employeeQualifications.map((q) => q.service_id);
		return services.filter((service) => !assignedServiceIds.includes(service.id));
	};

	// Group qualifications by employee, but include all employees
	const employeeQualifications = employees.reduce((acc, employee) => {
		const employeeName = `${employee.first_name} ${employee.last_name}`;
		const employeeQuals = qualifications.filter((qual) => qual.employee_id === employee.id);
		acc[employeeName] = {
			employee,
			qualifications: employeeQuals,
		};
		return acc;
	}, {} as Record<string, { employee: Employee; qualifications: EmployeeServiceQualification[] }>);

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-semibold">Qualifications des employés</h3>
				<p className="text-sm text-gray-600">Assignation des services aux employés</p>
			</div>

			{/* Qualifications by Employee */}
			<div className="space-y-4">
				{Object.entries(employeeQualifications).map(
					([employeeName, { employee, qualifications: employeeQuals }]) => (
						<Card key={employee.id}>
							<CardHeader>
								<CardTitle className="flex items-center justify-between">
									<div className="flex items-center gap-2">
										<User className="w-5 h-5" />
										{employeeName}
										<Badge variant="outline">{employeeQuals.length} service(s)</Badge>
										{employeeQuals.length === 0 && (
											<Badge variant="secondary">Aucun service assigné</Badge>
										)}
									</div>
									{getAvailableServices(employee.id).length > 0 && (
										<Button
											variant="outline"
											size="sm"
											onClick={() => setAddingToEmployee(employee.id)}
											className="flex items-center gap-1"
										>
											<Plus className="w-4 h-4" />
											Ajouter service
										</Button>
									)}
								</CardTitle>
							</CardHeader>
							<CardContent>
								{employeeQuals.length > 0 ? (
									<div className="space-y-2">
										{employeeQuals.map((qual) => (
											<div
												key={qual.id}
												className="flex items-center justify-between p-3 border rounded-lg"
											>
												<div className="flex items-center gap-3">
													<div className="flex items-center gap-2">
														{qual.is_active ? (
															<CheckCircle className="w-4 h-4 text-green-500" />
														) : (
															<XCircle className="w-4 h-4 text-red-500" />
														)}
														<span className="font-medium">
															{getServiceName(qual.service_id)}
														</span>
													</div>
													<Badge variant={qual.is_active ? "default" : "secondary"}>
														{qual.is_active ? "Actif" : "Inactif"}
													</Badge>
												</div>
												<div className="flex items-center gap-2">
													<Switch
														checked={qual.is_active}
														onCheckedChange={(checked) =>
															handleToggleActive(qual.id, checked)
														}
													/>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleDelete(qual.id)}
													>
														<Trash2 className="w-4 h-4" />
													</Button>
												</div>
											</div>
										))}
									</div>
								) : (
									<div className="p-4 text-center text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
										<p className="text-sm">Aucun service assigné à cet employé</p>
										<p className="text-xs text-gray-400 mt-1">
											Cliquez sur "Ajouter service" pour assigner des services
										</p>
									</div>
								)}

								{/* Inline Service Addition */}
								{addingToEmployee === employee.id && (
									<div className="mt-4 p-4 border-2 border-dashed border-blue-200 rounded-lg bg-blue-50">
										<div className="flex items-center gap-2">
											<Select value={selectedService} onValueChange={setSelectedService}>
												<SelectTrigger className="flex-1">
													<SelectValue placeholder="Sélectionner un service à ajouter" />
												</SelectTrigger>
												<SelectContent>
													{getAvailableServices(employee.id).map((service) => (
														<SelectItem key={service.id} value={service.id}>
															{service.name}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<Button
												onClick={() => handleAddService(employee.id)}
												disabled={!selectedService}
												size="sm"
											>
												Ajouter
											</Button>
											<Button
												variant="outline"
												onClick={() => {
													setAddingToEmployee(null);
													setSelectedService("");
												}}
												size="sm"
											>
												Annuler
											</Button>
										</div>
									</div>
								)}
							</CardContent>
						</Card>
					)
				)}

				{employees.length === 0 && !loading && (
					<Card>
						<CardContent className="p-8 text-center text-gray-500">
							<User className="w-12 h-12 mx-auto mb-4 opacity-50" />
							<p>Aucun employé trouvé</p>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
