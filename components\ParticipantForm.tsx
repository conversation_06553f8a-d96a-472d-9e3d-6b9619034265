"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Trash2, User } from 'lucide-react'
import { ParticipantInfo, PricingTier } from '@/lib/types'

interface ParticipantFormProps {
  participant: ParticipantInfo
  index: number
  onChange: (participant: ParticipantInfo) => void
  onRemove?: () => void
  pricingTiers: PricingTier[]
}

export default function ParticipantForm({
  participant,
  index,
  onChange,
  onRemove,
  pricingTiers
}: ParticipantFormProps) {
  
  const updateField = (field: keyof ParticipantInfo, value: any) => {
    onChange({
      ...participant,
      [field]: value
    })
  }

  // Find the appropriate pricing tier for the participant's age
  const getCurrentPricingTier = () => {
    return pricingTiers.find(tier => 
      participant.age >= tier.min_age && 
      (tier.max_age === null || participant.age <= tier.max_age)
    )
  }

  const currentTier = getCurrentPricingTier()

  // Get age category display
  const getAgeCategory = () => {
    if (!currentTier) return 'Âge non valide'
    return currentTier.tier_name
  }

  // Get price for this participant
  const getPrice = () => {
    return currentTier ? currentTier.price : 0
  }

  return (
    <Card className="relative">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <User className="w-4 h-4" />
            Participant {index + 1}
          </CardTitle>
          <div className="flex items-center gap-2">
            {currentTier && (
              <Badge variant="secondary">
                {getAgeCategory()} - {getPrice()}€
              </Badge>
            )}
            {onRemove && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={onRemove}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor={`firstName-${index}`}>Prénom *</Label>
            <Input
              id={`firstName-${index}`}
              value={participant.firstName}
              onChange={(e) => updateField('firstName', e.target.value)}
              placeholder="Prénom"
              required
            />
          </div>
          
          <div>
            <Label htmlFor={`lastName-${index}`}>Nom *</Label>
            <Input
              id={`lastName-${index}`}
              value={participant.lastName}
              onChange={(e) => updateField('lastName', e.target.value)}
              placeholder="Nom"
              required
            />
          </div>
          
          <div>
            <Label htmlFor={`age-${index}`}>Âge *</Label>
            <Input
              id={`age-${index}`}
              type="number"
              min="0"
              max="120"
              value={participant.age}
              onChange={(e) => updateField('age', parseInt(e.target.value) || 0)}
              placeholder="Âge"
              required
            />
          </div>
        </div>

        {/* Age-based pricing info */}
        {participant.age > 0 && (
          <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-800">
                  Catégorie: {getAgeCategory()}
                </p>
                {currentTier && (
                  <p className="text-xs text-emerald-600">
                    {currentTier.min_age} - {currentTier.max_age || '∞'} ans
                  </p>
                )}
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-emerald-800">
                  {getPrice()}€
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Age validation warnings */}
        {participant.age > 0 && !currentTier && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800">
              Aucun tarif disponible pour cet âge. Veuillez vérifier l'âge saisi.
            </p>
          </div>
        )}

        {/* Special Requirements */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor={`dietary-${index}`}>Restrictions alimentaires</Label>
            <Textarea
              id={`dietary-${index}`}
              value={participant.dietaryRestrictions || ''}
              onChange={(e) => updateField('dietaryRestrictions', e.target.value)}
              placeholder="Allergies, régimes spéciaux..."
              rows={2}
            />
          </div>
          
          <div>
            <Label htmlFor={`medical-${index}`}>Conditions médicales</Label>
            <Textarea
              id={`medical-${index}`}
              value={participant.medicalConditions || ''}
              onChange={(e) => updateField('medicalConditions', e.target.value)}
              placeholder="Conditions médicales importantes..."
              rows={2}
            />
          </div>
        </div>

        {/* Age-specific warnings */}
        {participant.age > 0 && (
          <>
            {participant.age < 3 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-sm text-yellow-800">
                  ⚠️ Les enfants de moins de 3 ans nécessitent une attention particulière et doivent être accompagnés d'un adulte en permanence.
                </p>
              </div>
            )}
            
            {participant.age >= 3 && participant.age < 12 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  ℹ️ Les enfants doivent être accompagnés d'un adulte responsable.
                </p>
              </div>
            )}
            
            {participant.age > 75 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <p className="text-sm text-orange-800">
                  ⚠️ Veuillez vous assurer que cette activité convient aux seniors. N'hésitez pas à nous contacter pour plus d'informations.
                </p>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
