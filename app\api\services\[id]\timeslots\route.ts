import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    const participants = parseInt(searchParams.get('participants') || '1')

    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      )
    }

    // Validate date format
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      )
    }

    // Validate participant count
    if (participants < 1 || participants > 50) {
      return NextResponse.json(
        { error: 'Participant count must be between 1 and 50' },
        { status: 400 }
      )
    }

    const serviceId = params.id

    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }

    // Get service details
    const { data: service, error: serviceError } = await supabaseAdmin
      .from('services')
      .select('id, name, duration_minutes, max_participants, buffer_time_minutes')
      .eq('id', serviceId)
      .eq('is_active', true)
      .single()

    if (serviceError || !service) {
      return NextResponse.json(
        { error: 'Service not found' },
        { status: 404 }
      )
    }

    // Check if participant count exceeds service capacity
    if (participants > service.max_participants) {
      return NextResponse.json({
        success: true,
        data: [],
        message: `Service capacity is ${service.max_participants} participants`
      })
    }

    // Get scheduling rules for the service
    const { data: rules, error: rulesError } = await supabaseAdmin
      .from('service_scheduling_rules')
      .select('*')
      .eq('service_id', serviceId)
      .eq('is_active', true)

    if (rulesError) {
      console.error('Error fetching scheduling rules:', rulesError)
      return NextResponse.json(
        { error: 'Failed to fetch scheduling rules' },
        { status: 500 }
      )
    }

    // If no rules exist, return empty array
    if (!rules || rules.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
        message: 'No scheduling rules configured for this service'
      })
    }

    // Generate time slots based on rules
    const timeSlots = generateTimeSlots(service, rules, date)

    // Get existing reservations for the date
    const { data: reservations, error: reservationsError } = await supabaseAdmin
      .from('reservations')
      .select('start_time, end_time, participant_count, status')
      .eq('service_id', serviceId)
      .gte('start_time', `${date}T00:00:00`)
      .lt('start_time', `${date}T23:59:59`)
      .in('status', ['confirmed', 'pending'])

    if (reservationsError) {
      console.error('Error fetching reservations:', reservationsError)
      return NextResponse.json(
        { error: 'Failed to fetch reservations' },
        { status: 500 }
      )
    }

    // Calculate availability for each time slot
    const availableSlots = timeSlots.map(slot => {
      const conflictingReservations = reservations?.filter(reservation => {
        const resStart = new Date(reservation.start_time)
        const resEnd = new Date(reservation.end_time)
        const slotStart = new Date(slot.start_time)
        const slotEnd = new Date(slot.end_time)

        // Check for time overlap
        return (slotStart < resEnd && slotEnd > resStart)
      }) || []

      const bookedParticipants = conflictingReservations.reduce(
        (total, res) => total + (res.participant_count || 0), 
        0
      )

      const availableCapacity = service.max_participants - bookedParticipants
      const canAccommodate = availableCapacity >= participants

      return {
        start_time: slot.start_time,
        end_time: slot.end_time,
        available_capacity: availableCapacity,
        is_available: canAccommodate && availableCapacity > 0,
        total_capacity: service.max_participants,
        booked_participants: bookedParticipants
      }
    })

    // Filter only available slots and sort by time
    const finalSlots = availableSlots
      .filter(slot => slot.is_available)
      .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())

    return NextResponse.json({
      success: true,
      data: finalSlots,
      service: {
        id: service.id,
        name: service.name,
        duration_minutes: service.duration_minutes,
        max_participants: service.max_participants
      }
    })

  } catch (error) {
    console.error('Error getting available time slots:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateTimeSlots(service: any, rules: any[], date: string) {
  const slots = []
  const dateObj = new Date(date)
  const dayOfWeek = dateObj.getDay()
  const now = new Date()

  for (const rule of rules) {
    // Check if rule applies to this day of week
    if (rule.day_of_week !== null && rule.day_of_week !== dayOfWeek) {
      continue
    }

    // Check advance booking requirements
    const hoursUntilDate = (dateObj.getTime() - now.getTime()) / (1000 * 60 * 60)
    if (hoursUntilDate < rule.min_advance_booking_hours) {
      continue
    }

    const daysUntilDate = hoursUntilDate / 24
    if (daysUntilDate > rule.max_advance_booking_days) {
      continue
    }

    // Generate slots based on rule type
    if (rule.specific_times && rule.specific_times.length > 0) {
      // Fixed-time scheduling
      for (const timeStr of rule.specific_times) {
        const [hours, minutes] = timeStr.split(':').map(Number)
        const startTime = new Date(dateObj)
        startTime.setHours(hours, minutes, 0, 0)

        const endTime = new Date(startTime)
        endTime.setMinutes(endTime.getMinutes() + service.duration_minutes)

        slots.push({
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString()
        })
      }
    } else if (rule.booking_interval_minutes) {
      // Interval-based scheduling
      const [startHour, startMin] = (rule.operating_start_time || '09:00').split(':').map(Number)
      const [endHour, endMin] = (rule.operating_end_time || '17:00').split(':').map(Number)

      const operatingStart = startHour * 60 + startMin
      const operatingEnd = endHour * 60 + endMin

      for (let currentMin = operatingStart; currentMin < operatingEnd; currentMin += rule.booking_interval_minutes) {
        const startTime = new Date(dateObj)
        startTime.setHours(Math.floor(currentMin / 60), currentMin % 60, 0, 0)

        const endTime = new Date(startTime)
        endTime.setMinutes(endTime.getMinutes() + service.duration_minutes)

        // Check if end time is within operating hours
        const endMinutes = endTime.getHours() * 60 + endTime.getMinutes()
        if (endMinutes <= operatingEnd) {
          slots.push({
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString()
          })
        }
      }
    }
  }

  // Remove duplicates and sort
  const uniqueSlots = slots
    .filter((slot, index, self) => 
      index === self.findIndex(s => s.start_time === slot.start_time)
    )
    .sort((a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime())

  return uniqueSlots
}
