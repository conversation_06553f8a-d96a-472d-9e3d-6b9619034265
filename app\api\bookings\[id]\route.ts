import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const bookingId = params.id

    // Fetch reservation with related data
    const { data: reservation, error } = await supabase
      .from('reservations')
      .select(`
        *,
        service:services (
          id,
          name,
          image_url,
          duration_minutes
        ),
        customer:customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        participants:reservation_participants (
          id,
          first_name,
          last_name,
          age,
          individual_price
        )
      `)
      .eq('id', bookingId)
      .single()

    if (error || !reservation) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      )
    }

    // Transform data for confirmation page
    const bookingData = {
      bookingNumber: reservation.qr_code,
      customerName: `${reservation.customer.first_name} ${reservation.customer.last_name}`,
      email: reservation.customer.email,
      phone: reservation.customer.phone,
      bookingDate: reservation.created_at,
      totalAmount: reservation.total_amount,
      qrCode: reservation.qr_code,
      items: [{
        service: reservation.service.name,
        date: new Date(reservation.start_time).toISOString().split('T')[0],
        time: new Date(reservation.start_time).toLocaleTimeString('fr-FR', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        participants: reservation.total_participants,
        price: reservation.total_amount,
        image: reservation.service.image_url || '/placeholder.svg'
      }]
    }

    return NextResponse.json({
      success: true,
      data: bookingData
    })

  } catch (error) {
    console.error('Error fetching booking:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
