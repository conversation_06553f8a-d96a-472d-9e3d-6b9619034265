"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { adminApi } from "@/lib/api-client";
import { Edit, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";

interface ServiceSchedulingRule {
	id: string;
	service_id: string;
	day_of_week: number | null;
	min_advance_booking_hours: number;
	max_advance_booking_days: number;
	operating_start_time: string;
	operating_end_time: string;
	booking_interval_minutes: number | null;
	specific_times: string[] | null;
	max_bookings_per_day: number | null;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

interface ServiceSchedulingRulesProps {
	serviceId: string;
	serviceName: string;
}

const DAYS_OF_WEEK = [
	{ value: null, label: "Tous les jours" },
	{ value: 0, label: "Dimanche" },
	{ value: 1, label: "Lundi" },
	{ value: 2, label: "Mardi" },
	{ value: 3, label: "Mercredi" },
	{ value: 4, label: "Jeudi" },
	{ value: 5, label: "Vendredi" },
	{ value: 6, label: "Samedi" },
];

export default function ServiceSchedulingRules({ serviceId, serviceName }: ServiceSchedulingRulesProps) {
	const [rules, setRules] = useState<ServiceSchedulingRule[]>([]);
	const [loading, setLoading] = useState(true);
	const [editing, setEditing] = useState<string | null>(null);
	const [creating, setCreating] = useState(false);
	const [formData, setFormData] = useState<Partial<ServiceSchedulingRule>>({});

	useEffect(() => {
		fetchRules();
	}, [serviceId]);

	const fetchRules = async () => {
		try {
			const data = await adminApi.getServiceSchedulingRules(serviceId);
			setRules(data.rules || []);
		} catch (error) {
			console.error("Error fetching scheduling rules:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setCreating(true);
		setFormData({
			service_id: serviceId,
			day_of_week: null,
			min_advance_booking_hours: 2,
			max_advance_booking_days: 30,
			operating_start_time: "08:00",
			operating_end_time: "18:00",
			booking_interval_minutes: 60,
			specific_times: null,
			max_bookings_per_day: null,
			is_active: true,
		});
	};

	const handleEdit = (rule: ServiceSchedulingRule) => {
		setEditing(rule.id);
		setFormData({ ...rule });
	};

	const handleSave = async () => {
		try {
			if (creating) {
				await adminApi.createServiceSchedulingRule(serviceId, formData);
			} else {
				await adminApi.updateServiceSchedulingRule(serviceId, editing!, formData);
			}

			await fetchRules();
			handleCancel();
		} catch (error) {
			console.error("Error saving rule:", error);
		}
	};

	const handleDelete = async (ruleId: string) => {
		if (confirm("Êtes-vous sûr de vouloir supprimer cette règle ?")) {
			try {
				await adminApi.deleteServiceSchedulingRule(serviceId, ruleId);
				await fetchRules();
			} catch (error) {
				console.error("Error deleting rule:", error);
			}
		}
	};

	const handleCancel = () => {
		setCreating(false);
		setEditing(null);
		setFormData({});
	};

	const formatDayOfWeek = (dayOfWeek: number | null) => {
		return DAYS_OF_WEEK.find((d) => d.value === dayOfWeek)?.label || "Tous les jours";
	};

	const formatScheduleType = (rule: ServiceSchedulingRule) => {
		if (rule.specific_times && rule.specific_times.length > 0) {
			return `Heures fixes: ${rule.specific_times.join(", ")}`;
		}
		if (rule.booking_interval_minutes) {
			return `Intervalles de ${rule.booking_interval_minutes} min`;
		}
		return "Non configuré";
	};

	if (loading) {
		return <div className="p-4">Chargement...</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h3 className="text-lg font-semibold">Règles de planification</h3>
					<p className="text-sm text-gray-600">Service: {serviceName}</p>
				</div>
				<Button onClick={handleCreate} className="flex items-center gap-2">
					<Plus className="w-4 h-4" />
					Nouvelle règle
				</Button>
			</div>

			{/* Rules List */}
			<div className="space-y-4">
				{rules.map((rule) => (
					<Card key={rule.id}>
						<CardContent className="p-4">
							{editing === rule.id ? (
								<RuleForm
									formData={formData}
									setFormData={setFormData}
									onSave={handleSave}
									onCancel={handleCancel}
								/>
							) : (
								<div className="flex justify-between items-start">
									<div className="space-y-2">
										<div className="flex items-center gap-2">
											<Badge variant={rule.is_active ? "default" : "secondary"}>
												{rule.is_active ? "Actif" : "Inactif"}
											</Badge>
											<span className="font-medium">{formatDayOfWeek(rule.day_of_week)}</span>
										</div>
										<div className="text-sm text-gray-600">
											<div>
												Horaires: {rule.operating_start_time} - {rule.operating_end_time}
											</div>
											<div>{formatScheduleType(rule)}</div>
											<div>
												Réservation: {rule.min_advance_booking_hours}h à{" "}
												{rule.max_advance_booking_days}j à l'avance
											</div>
											{rule.max_bookings_per_day && (
												<div>Max {rule.max_bookings_per_day} réservations/jour</div>
											)}
										</div>
									</div>
									<div className="flex gap-2">
										<Button variant="outline" size="sm" onClick={() => handleEdit(rule)}>
											<Edit className="w-4 h-4" />
										</Button>
										<Button variant="outline" size="sm" onClick={() => handleDelete(rule.id)}>
											<Trash2 className="w-4 h-4" />
										</Button>
									</div>
								</div>
							)}
						</CardContent>
					</Card>
				))}
			</div>

			{/* Create Form */}
			{creating && (
				<Card>
					<CardHeader>
						<CardTitle>Nouvelle règle de planification</CardTitle>
					</CardHeader>
					<CardContent>
						<RuleForm
							formData={formData}
							setFormData={setFormData}
							onSave={handleSave}
							onCancel={handleCancel}
						/>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

interface RuleFormProps {
	formData: Partial<ServiceSchedulingRule>;
	setFormData: (data: Partial<ServiceSchedulingRule>) => void;
	onSave: () => void;
	onCancel: () => void;
}

function RuleForm({ formData, setFormData, onSave, onCancel }: RuleFormProps) {
	return (
		<div className="space-y-4">
			<div className="grid grid-cols-2 gap-4">
				<div>
					<Label>Jour de la semaine</Label>
					<Select
						value={formData.day_of_week?.toString() || "null"}
						onValueChange={(value) =>
							setFormData({
								...formData,
								day_of_week: value === "null" ? null : parseInt(value),
							})
						}
					>
						<SelectTrigger>
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							{DAYS_OF_WEEK.map((day) => (
								<SelectItem
									key={day.value?.toString() || "null"}
									value={day.value?.toString() || "null"}
								>
									{day.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="flex items-center space-x-2">
					<Switch
						checked={formData.is_active || false}
						onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
					/>
					<Label>Règle active</Label>
				</div>
			</div>

			<div className="grid grid-cols-2 gap-4">
				<div>
					<Label>Heure de début</Label>
					<Input
						type="time"
						value={formData.operating_start_time || ""}
						onChange={(e) => setFormData({ ...formData, operating_start_time: e.target.value })}
					/>
				</div>
				<div>
					<Label>Heure de fin</Label>
					<Input
						type="time"
						value={formData.operating_end_time || ""}
						onChange={(e) => setFormData({ ...formData, operating_end_time: e.target.value })}
					/>
				</div>
			</div>

			<div className="grid grid-cols-3 gap-4">
				<div>
					<Label>Réservation min (heures)</Label>
					<Input
						type="number"
						value={formData.min_advance_booking_hours || ""}
						onChange={(e) =>
							setFormData({ ...formData, min_advance_booking_hours: parseInt(e.target.value) })
						}
					/>
				</div>
				<div>
					<Label>Réservation max (jours)</Label>
					<Input
						type="number"
						value={formData.max_advance_booking_days || ""}
						onChange={(e) =>
							setFormData({ ...formData, max_advance_booking_days: parseInt(e.target.value) })
						}
					/>
				</div>
				<div>
					<Label>Max réservations/jour</Label>
					<Input
						type="number"
						value={formData.max_bookings_per_day || ""}
						onChange={(e) =>
							setFormData({
								...formData,
								max_bookings_per_day: e.target.value ? parseInt(e.target.value) : null,
							})
						}
					/>
				</div>
			</div>

			<div>
				<Label>Intervalle de réservation (minutes)</Label>
				<Input
					type="number"
					value={formData.booking_interval_minutes || ""}
					onChange={(e) =>
						setFormData({
							...formData,
							booking_interval_minutes: e.target.value ? parseInt(e.target.value) : null,
						})
					}
					placeholder="Laisser vide pour heures fixes"
				/>
			</div>

			<div className="flex gap-2">
				<Button onClick={onSave} className="flex items-center gap-2">
					<Save className="w-4 h-4" />
					Sauvegarder
				</Button>
				<Button variant="outline" onClick={onCancel} className="flex items-center gap-2">
					<X className="w-4 h-4" />
					Annuler
				</Button>
			</div>
		</div>
	);
}
