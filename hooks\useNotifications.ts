import { useEffect, useState } from 'react'
import { adminApi } from '@/lib/api-client'

export interface NotificationCounts {
  total: number
  unread: number
}

export function useNotificationCounts() {
  const [counts, setCounts] = useState<NotificationCounts>({ total: 0, unread: 0 })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCounts = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch notifications to count them
      const result = await adminApi.getNotifications({ limit: 100 }) // Get enough to count accurately
      
      if (result.success) {
        const total = result.data.length
        const unread = result.data.filter((notif: any) => !notif.is_read).length
        
        setCounts({ total, unread })
      } else {
        throw new Error(result.error || 'Failed to fetch notifications')
      }
    } catch (err) {
      console.error('Error fetching notification counts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch notification counts')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCounts()
  }, [])

  return {
    counts,
    loading,
    error,
    refetch: fetchCounts
  }
}

export function useNotifications() {
  const [notifications, setNotifications] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const result = await adminApi.getNotifications()
      
      if (result.success) {
        setNotifications(result.data)
      } else {
        throw new Error(result.error || 'Failed to fetch notifications')
      }
    } catch (err) {
      console.error('Error fetching notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications')
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      await adminApi.updateNotification(id, { is_read: true })
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === id ? { ...notif, is_read: true } : notif
        )
      )
    } catch (err) {
      console.error('Error marking notification as read:', err)
      throw err
    }
  }

  const deleteNotification = async (id: string) => {
    try {
      await adminApi.deleteNotification(id)
      
      // Update local state
      setNotifications(prev => prev.filter(notif => notif.id !== id))
    } catch (err) {
      console.error('Error deleting notification:', err)
      throw err
    }
  }

  const markAllAsRead = async () => {
    try {
      const unreadIds = notifications.filter(n => !n.is_read).map(n => n.id)
      if (unreadIds.length === 0) return

      await adminApi.markNotificationsAsRead(unreadIds)
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ ...notif, is_read: true }))
      )
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
      throw err
    }
  }

  useEffect(() => {
    fetchNotifications()
  }, [])

  return {
    notifications,
    loading,
    error,
    refetch: fetchNotifications,
    markAsRead,
    deleteNotification,
    markAllAsRead
  }
}
