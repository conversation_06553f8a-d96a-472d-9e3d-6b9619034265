import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { Database } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

type Notification = Database["public"]["Tables"]["notifications"]["Row"];
type NotificationUpdate = Database["public"]["Tables"]["notifications"]["Update"];

// GET /api/admin/notifications/[id] - Get single notification
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const notificationId = params.id;

		const { data: notification, error } = await supabaseAdmin
			.from("notifications")
			.select(
				`
        *,
        recipient:profiles!recipient_id(first_name, last_name, email),
        reservation:reservations(id, qr_code)
      `
			)
			.eq("id", notificationId)
			.single();

		if (error) {
			if (error.code === "PGRST116") {
				return NextResponse.json({ error: "Notification not found" }, { status: 404 });
			}
			console.error("Error fetching notification:", error);
			return NextResponse.json({ error: "Failed to fetch notification" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			data: notification,
		});
	} catch (error) {
		console.error("Notification GET error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "notifications:read");

// PATCH /api/admin/notifications/[id] - Update notification (mark as read, update content, etc.)
export const PATCH = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const notificationId = params.id;
		const body = await request.json();

		// Get current notification for logging
		const { data: currentNotification, error: fetchError } = await supabaseAdmin
			.from("notifications")
			.select("*")
			.eq("id", notificationId)
			.single();

		if (fetchError) {
			if (fetchError.code === "PGRST116") {
				return NextResponse.json({ error: "Notification not found" }, { status: 404 });
			}
			console.error("Error fetching notification:", fetchError);
			return NextResponse.json({ error: "Failed to fetch notification" }, { status: 500 });
		}

		// Prepare update data
		const updateData: NotificationUpdate = {};

		// Handle marking as read
		if (body.is_read !== undefined) {
			updateData.is_read = body.is_read;
			if (body.is_read) {
				updateData.read_at = new Date().toISOString();
			} else {
				updateData.read_at = null;
			}
		}

		// Handle content updates
		if (body.subject !== undefined) updateData.subject = body.subject;
		if (body.content !== undefined) updateData.content = body.content;
		if (body.priority !== undefined) {
			const validPriorities = ["low", "medium", "high"];
			if (!validPriorities.includes(body.priority)) {
				return NextResponse.json(
					{
						error: `Invalid priority. Must be one of: ${validPriorities.join(", ")}`,
					},
					{ status: 400 }
				);
			}
			updateData.priority = body.priority;
		}

		// Handle status updates
		if (body.status !== undefined) {
			const validStatuses = ["pending", "sent", "failed", "bounced"];
			if (!validStatuses.includes(body.status)) {
				return NextResponse.json(
					{
						error: `Invalid status. Must be one of: ${validStatuses.join(", ")}`,
					},
					{ status: 400 }
				);
			}
			updateData.status = body.status;
			if (body.status === "sent" && !updateData.sent_at) {
				updateData.sent_at = new Date().toISOString();
			}
		}

		if (body.error_message !== undefined) updateData.error_message = body.error_message;

		const { data: notification, error } = await supabaseAdmin
			.from("notifications")
			.update(updateData)
			.eq("id", notificationId)
			.select(
				`
        *,
        recipient:profiles!recipient_id(first_name, last_name, email),
        reservation:reservations(id, qr_code)
      `
			)
			.single();

		if (error) {
			console.error("Error updating notification:", error);
			return NextResponse.json({ error: "Failed to update notification" }, { status: 500 });
		}

		// TODO: Add admin action logging if needed

		return NextResponse.json({
			success: true,
			data: notification,
		});
	} catch (error) {
		console.error("Notification PATCH error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "notifications:write");

// DELETE /api/admin/notifications/[id] - Delete notification
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const notificationId = params.id;

		// Get notification for logging before deletion
		const { data: notification, error: fetchError } = await supabaseAdmin
			.from("notifications")
			.select("*")
			.eq("id", notificationId)
			.single();

		if (fetchError) {
			if (fetchError.code === "PGRST116") {
				return NextResponse.json({ error: "Notification not found" }, { status: 404 });
			}
			console.error("Error fetching notification:", fetchError);
			return NextResponse.json({ error: "Failed to fetch notification" }, { status: 500 });
		}

		const { error } = await supabaseAdmin.from("notifications").delete().eq("id", notificationId);

		if (error) {
			console.error("Error deleting notification:", error);
			return NextResponse.json({ error: "Failed to delete notification" }, { status: 500 });
		}

		// TODO: Add admin action logging if needed

		return NextResponse.json({
			success: true,
			message: "Notification deleted successfully",
		});
	} catch (error) {
		console.error("Notification DELETE error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}, "notifications:write");
