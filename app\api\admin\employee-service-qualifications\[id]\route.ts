import { withAdminAuth } from '@/lib/admin-auth'
import { supabaseAdmin } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

// PUT /api/admin/employee-service-qualifications/[id] - Update qualification
export const PUT = withAdminAuth(async (
  request: NextRequest, 
  user, 
  { params }: { params: { id: string } }
) => {
  try {
    const qualificationId = params.id
    const qualificationData = await request.json()

    const { data: qualification, error } = await supabaseAdmin
      .from('employee_service_qualifications')
      .update(qualificationData)
      .eq('id', qualificationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating qualification:', error)
      return NextResponse.json({ error: 'Failed to update qualification' }, { status: 500 })
    }

    if (!qualification) {
      return NextResponse.json({ error: 'Qualification not found' }, { status: 404 })
    }

    return NextResponse.json({ qualification })
  } catch (error) {
    console.error('Qualification PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'employees:write')

// DELETE /api/admin/employee-service-qualifications/[id] - Delete qualification
export const DELETE = withAdminAuth(async (
  request: NextRequest, 
  user, 
  { params }: { params: { id: string } }
) => {
  try {
    const qualificationId = params.id

    const { error } = await supabaseAdmin
      .from('employee_service_qualifications')
      .delete()
      .eq('id', qualificationId)

    if (error) {
      console.error('Error deleting qualification:', error)
      return NextResponse.json({ error: 'Failed to delete qualification' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Qualification DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'employees:write')
