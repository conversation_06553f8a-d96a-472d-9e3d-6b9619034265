import { withAdminAuth } from '@/lib/admin-auth'
import { supabaseAdmin } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

// PUT /api/admin/services/[id]/blackout-dates/[blackoutId] - Update blackout date
export const PUT = withAdminAuth(async (
  request: NextRequest, 
  user, 
  { params }: { params: { id: string; blackoutId: string } }
) => {
  try {
    const { id: serviceId, blackoutId } = params
    const blackoutData = await request.json()

    // Validate date range
    if (blackoutData.start_date && blackoutData.end_date && 
        new Date(blackoutData.start_date) > new Date(blackoutData.end_date)) {
      return NextResponse.json(
        { error: 'Start date must be before or equal to end date' },
        { status: 400 }
      )
    }

    // Ensure service_id matches the URL parameter
    blackoutData.service_id = serviceId

    const { data: blackoutDate, error } = await supabaseAdmin
      .from('service_blackout_dates')
      .update(blackoutData)
      .eq('id', blackoutId)
      .eq('service_id', serviceId)
      .select()
      .single()

    if (error) {
      console.error('Error updating blackout date:', error)
      return NextResponse.json({ error: 'Failed to update blackout date' }, { status: 500 })
    }

    if (!blackoutDate) {
      return NextResponse.json({ error: 'Blackout date not found' }, { status: 404 })
    }

    return NextResponse.json({ blackoutDate })
  } catch (error) {
    console.error('Blackout date PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')

// DELETE /api/admin/services/[id]/blackout-dates/[blackoutId] - Delete blackout date
export const DELETE = withAdminAuth(async (
  request: NextRequest, 
  user, 
  { params }: { params: { id: string; blackoutId: string } }
) => {
  try {
    const { id: serviceId, blackoutId } = params

    const { error } = await supabaseAdmin
      .from('service_blackout_dates')
      .delete()
      .eq('id', blackoutId)
      .eq('service_id', serviceId)

    if (error) {
      console.error('Error deleting blackout date:', error)
      return NextResponse.json({ error: 'Failed to delete blackout date' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Blackout date DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')
